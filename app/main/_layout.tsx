import { StatusBar } from 'expo-status-bar';

import { useUserProfile } from '@/common/profile';
import { withUserProfile } from '@/common/withUserProfile';
import FullScreenActivityIndicator from '@/components/FullScreenActivityIndicator';
import { JsStack } from '@/components/global/JsStack';
import { ModalProvider } from '@/components/global/modal';
import { RevenueCatProvider } from '@/hooks/usePurchases';
import { useThemeColorLegacy, useThemeLinearGradient } from '@/hooks/useThemeColor';

function Layout() {
  const theme = useThemeColorLegacy();
  const gradientColors = useThemeLinearGradient();
  const { profile } = useUserProfile();

  if (!profile) return <FullScreenActivityIndicator />;

  // Use the first gradient color as the status bar background
  const statusBarBackgroundColor = gradientColors[0];

  return (
    <RevenueCatProvider>
      <ModalProvider>
        <StatusBar backgroundColor={statusBarBackgroundColor} style={theme.style === 'light' ? 'dark' : 'light'} />
        <JsStack initialRouteName="app" screenOptions={{ headerShown: false }}>
          <JsStack.Screen name="onboarding" options={{ presentation: 'modal' }} />
          <JsStack.Screen name="settings" options={{ presentation: 'modal' }} />
          <JsStack.Screen name="company" options={{ headerShown: false }} />
        </JsStack>
      </ModalProvider>
    </RevenueCatProvider>
  );
}

export default withUserProfile(Layout);
