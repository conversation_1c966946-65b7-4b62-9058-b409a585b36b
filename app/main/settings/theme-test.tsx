import React from 'react';

import { SafeAreaView, StyleSheet, Text, View } from 'react-native';

import { ThemeSelector } from '@/components/ThemeSelector';
import { ThemedText } from '@/components/global/ThemedText';
import { ThemedView } from '@/components/global/ThemedView';
import { useCurrentThemeColors, useThemeLinearGradient } from '@/hooks/useThemeColor';

export default function ThemeTestScreen() {
  const colors = useCurrentThemeColors();
  const gradientColors = useThemeLinearGradient();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <View style={styles.container}>
        <ThemedView style={styles.header}>
          <ThemedText type="bold" size={24}>
            Theme System Test
          </ThemedText>
          <ThemedText type="regular" size={16}>
            Test all 10 themes with system detection
          </ThemedText>
        </ThemedView>

        <View style={styles.content}>
          <ThemeSelector />
        </View>

        <ThemedView style={styles.footer}>
          <ThemedText type="semi-bold" size={14}>
            Gradient Colors: {gradientColors[0]} → {gradientColors[1]}
          </ThemedText>
        </ThemedView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  footer: {
    padding: 15,
    alignItems: 'center',
  },
});
