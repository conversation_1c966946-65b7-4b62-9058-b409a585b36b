name: Expo Build

on:
  workflow_dispatch:
    inputs:
      platform:
        description: 'Build Platform'
        required: true
        default: 'android'
        type: choice
        options:
          - android
          - ios

jobs:
  build:
    name: Build ${{ github.event.inputs.platform }}
    runs-on: ubuntu-latest

    steps:
      - name: 🏗 Checkout repository
        uses: actions/checkout@v3

      - name: 🏗 Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20.x
          cache: 'npm'

      - name: 🏗 Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Build for iOS
        if: github.event.inputs.platform == 'ios'
        run: eas build --profile production --platform ios --non-interactive

      - name: 🚀 Build for Android
        if: github.event.inputs.platform == 'android'
        run: eas build --profile production --platform android --non-interactive
