{"cta": "Import now!", "importViaSecapiImportId": "Import via SecAPI import ID", "confirmCancel": {"title": "Confirm closing portfolio import dialog?", "description": "If you close this dialog, but have already chosen your bank and entered all required credentials, this portfolio import will continue running in the background and you can view its progress and finalize it in the settings. Otherwise your progress will be reset. It is recommended to leave this dialog open until the import is finished. Click on 'OK' if you still want to close this dialog."}, "chooseBank": {"title": "Choose bank", "choose": "<PERSON><PERSON>", "otherBanks": "More national banks", "internationalBanks": "International banks", "otherBanksInternational": "More banks", "demoBanks": "Demo banks", "manualInput": "Input manually", "bankListLoading": "Loading list of supported banks..."}, "searchBankBranch": {"title": "Choose bank branch", "query": {"labels": {"bank": "Search for $t(bank:{{bank}}) branch", "other": "Search for bank"}, "helper": "You can search for the bank code, BIC or the name of your bank."}, "noResults": "No results. Please modify your search query.", "numbers": {"blz": "BLZ", "bic": "BIC"}}, "chooseInterface": {"title": "Choose interface", "recommended": "Recommended", "failed": "Failed", "interfaceType": {"DEMO": {"title": "Demo Interface", "description": "The demo interface demonstrates the functionality of Divizend.", "duration": "< 1 min"}, "NATIVE": {"title": "Native Interface", "description": "Using the native Open Banking interface of your bank, you can connect your securities account to Divizend within moments.\nThe access data to your securities account are transmitted to your bank for authentication using an encrypted protocol. Divizend has no access to your access data at any time.", "duration": "< 1 min"}, "FINAPI": {"title": "finAPI", "description": "finAPI is a german financial services provider licensed by BaFin that offers interfaces for accessing payment and securities accounts. By authenticating over finAPI, you can connect your securities account to Divizend within moments.\nThe access data to your securities account are transmitted by finAPI to your bank for authentication using an encrypted protocol. Divizend has no access to your access data at any time.", "duration": "< 1 min"}, "DOCAPI": {"title": "PDF import", "description": "With PDF import, you first upload portfolio overview documents and purchase receipts, which you can download as PDFs from your broker account mailbox, then Divizend automatically reads your current shareholdings and possibly your transaction history.", "duration": "> 2 min"}, "FINTS": {"title": "FinTS", "description": "FinTS is a german standard for accessing payment and securities accounts. We have specifically optimized our interface to determine your current securities account statement. By authenticating over FinTS, you can connect your securities account to Divizend within moments.\nThe access data to your securities account are transmitted to your bank exclusively and once for authentication using an encrypted protocol, they are not stored.", "duration": "< 1 min"}, "POCAPI": {"title": "PDF Import", "description": "The PDF import allows you to connect your securities account to Divizend even without using a banking interface. To do so, you can upload the relevant documents from your mailbox.\nThe documents are only read once to determine the current securities account statement and the transaction history, they are not stored.", "duration": "> 2 min"}, "SCRAPER": {"title": "Screen Analyzer", "description": "Using the Screen Analyzer, you can connect your securities account to Divizend using the graphical user interface within moments.\nThe access data to your securities account are transmitted to your bank exclusively and once for authentication using an encrypted protocol, they are not stored.", "duration": "< 1 min"}, "ESTA": {"title": "eSteuerauszug", "description": "Using this method, you can use an eSteuerauszug (electronic tax statement) to automatically import your securities account at Divizend. eSteuerauszüge can be found at many Swiss banks in your online banking as PDF documents.", "duration": "< 1 min"}, "FLANKS": {"title": "Flanks", "description": "Flanks is a licensed account information services provider that offers interfaces for open access to payment and securities accounts. Authentication via Flanks allows you to connect your securities account to Divizend within moments.", "duration": "< 1 min"}, "MANUAL": {"title": "Manual Input", "description": "Manual input allows you to connect your securities account to Divizend without any automated interfaces or documents. For this purpose, you enter your current shareholdings as well as your transaction history, if applicable, via our graphical interface.", "duration": "> 3 min"}}}, "bankLogin": {"title": "Connect portfolio", "error": "Connecting portfolio failed: {{error, error}}", "errorPlain": "Connecting portfolio failed: {{error}}", "newTabInfo": "A new tab should have opened where you can connect your bank to Divizend.", "clickToOpen": "If this is not the case, please click here.", "waitingForAggregation": "You have successfully connected your portfolio and it is now being scanned. Please wait a moment.", "chooseOtherMethod": "Choose another portfolio import method"}, "bankDepotDetails": {"title": "Bank and depot details", "description": "Please enter the following information about your bank and securities account.", "fields": {"parent": "Bank type", "bankName": "Bank name", "bic": "BIC", "depotNumber": "Your portfolio number"}, "unknownBank": "Other bank"}, "chooseDepots": {"title": "Choose portfolios", "description": "Please select the portfolios you want to import to Divizend:", "callToAction": "Next step", "depotNumber": "Portfolio number: {{number}}", "selectAll": "Select all", "selected": "Selected {{count}} portfolios out of {{total}}."}, "chooseDepotToSync": {"title": "Choose portfolio to synchronize", "description": "Please select the portfolio you want to use for the synchronization:", "infoNote": "We noticed a mismatch between your current portfolio number and the one(s) you are trying to use for synchronization."}, "portfolioContents": {"title": "Portfolio contents", "callToAction": "Import now", "importError": "Importing failed: {{error, error}}", "confirmIncompleteImport": {"title": "Confirm incomplete import", "description": "Some of your portfolio's securities still need further information so that they can be imported. If you would still like to proceed, the following highlighted incomplete securities <b>will not be imported</b>:", "descriptionCompleteWhenRemovingTx": "The following stocks have only an incomplete or inconsistent transaction history. If you want to proceed, only their transactions will be removed, but all other information about these stocks will be imported as usual:"}, "importBlocked": {"noSecuritiesExist": "Please add shares to this securities account with the + symbol on the left hand side.", "noSecuritiesComplete": "Please complete the data of the shares highlighted on the left hand side."}}, "background": {"title": "You can now close this dialog now", "description": "The import is running in the background, so you are free to close this import dialog.", "stay": "Stay"}, "finalize": {"title": "Continue", "heading": "Import successful! How shall we go on?", "headingNotDone": "Your portfolio was imported successfully!", "headingMultiple": "Imports successful!", "subheadingNotDone": "However, some details about your securities and dividends are still missing. Please click on \"Edit portfolio\" to fix this.", "importAnother": "Import another portfolio", "backToDashboard": "Go to the Maximizer dashboard", "editPortfolio": "Edit portfolio", "multiAccountFilter": "Please click on \"OK\" below now to assign all imported portfolios to the respective customers of your organization."}, "loadingDepot": "Loading your portfolio(s)...", "importingDepot": "Importing your portfolio(s)...", "loadingDepotSuccess": "Done!", "restartImport": "Restart import", "selectSecapiImportIdPlaceholder": "SecAPI import ID", "selectSecapiImportId": "Select the SecAPI import ID to import its content!"}