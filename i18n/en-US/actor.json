{"portfolioStats": {"title": "My portfolio", "totalAssets": "Total assets", "grossDividendThisYear": "Gross dividends this year", "totalRefundableWithholdingTax": "Total refundable withholding tax", "nextDividend": "Next dividend"}, "chartInstruction": "Hold and swipe to explore different dates and events", "division": {"title": "Division", "totalAssets": "Total Assets", "others": "Others"}, "simulation": {"title": "Simulation", "radioButton": {"1M": "1M", "3M": "3M", "6M": "6M", "Y": "Y"}, "scenarioButton": {"DOT_COM": "Dot-Com Bubble", "FIN_CRISIS_2007": "Financial Crisis 2007", "COVID": "COVID-19 Crash", "INFLATION_2021": "Inflation 2021", "BREXIT_UNCERTAINTY": "Brexit Uncertainty", "US_EU_STEEL_TARIFFS": "US–EU Steel Tariffs", "US_CHINA_TRADE_WAR": "US–China Trade War", "TRUMP_TARIFFS_2025": "Trump Tariffs 2025"}, "settings": {"title": "Settings", "showPercentage": "Show percentage", "options": "Options"}}, "quotes": {"title": "Performance overview", "radioButton": {"D": "D", "W": "W", "M": "M", "Y": "Y", "ALL": "All"}, "options": {"performance": "Performance", "twror": "TWROR (Time-Weighted Rate of Return)", "mwror": "MWROR (Money-Weighted Rate of Return)"}, "noQuotes": "No available quotes"}, "assetClasses": {"title": "Asset classes", "secType": {"STOCK": "Stock", "ETF": "ETF (Exchange-Traded Fund)", "FUND": "Fund", "DR": "DR (Depositary Receipt)", "BOND": "Bond", "OPTION": "Option", "CRYPTO": "Crypto Currency", "TRUST": "Trust", "CERTIFICATE": "Certificate", "OTHER": "Other", "undefined": "Unknown (Contain no issues)"}}, "calendarWidget": {"title": "Calendar", "tooltip": {"eventType": "Event type", "date": "Date", "exDate": "Ex-date", "paymentDate": "Payment date", "dividendPerShare": "Dividend per share", "frequency": "Frequency", "transactionType": "Type", "transactionQuantity": "Quantity", "transactionAmount": "Amount", "splitRatio": "Split ratio", "PURCHASE": "Buy", "SALE": "<PERSON>ll", "dividendYield": "Dividend yield"}, "DIVIDEND": "Dividend", "TRANSACTION": "Transaction", "SPLIT": "Split", "moreItems": "{{count}} more...", "predictionTooltip": "Prediction", "dividendPayout": "{{price}}/share", "transactionPrice": "{{quantity}} x {{price}}/share", "totalPayout": "Total {{total}}", "totalGrossDividend": "Total gross dividend: {{quantity}} x {{price}}/share = {{total}}", "noEvents": "No events this day"}, "portfolioSelector": {"all": "All portfolios", "label": "Portfolios selection", "select": "Select portfolios"}, "dividendHistory": {"title": "Company dividend history", "displayOption": {"title": "Display option", "ABSOLUTE": "Absolute", "ABSOLUTESPLITADJUSTED": "Absolute (split adjusted)", "YIELDS": "Yields"}, "yield": "Yield", "splitAdjusted": "Split adjusted", "atPrice": "at {{price}}", "absoluteInfo": "Dividend from {{date}}: <strong>{{price}} per share</strong>", "absoluteSplitAdjustedInfo": {"splits": {"value": "Accumulated splits since then: <strong>{{from}}:{{to}}</strong>", "splitAdjustedDividend": "Split-adjusted dividend: {{priceBeforeSplit}} * {{splits}} = <strong>{{price}}</strong> per share"}, "noSplits": "No splits happened since then."}, "yieldInfo": {"splits": {"dividendFrequency": "Dividend frequency: {{frequency}} → <strong>factor {{factor}}</strong>", "sharePrice": "Share price at {{date}}: <strong>{{price}}</strong>", "dividendYield": "Dividend yield: (({{yield}} * {{splits}}) * {{factor}}) / {{price}} = <strong>{{result}}</strong>"}, "dividendYield": "Dividend yield: {{yield}}  / {{price}} = <strong>{{result}}</strong>"}}, "sharePriceWidget": {"title": "Current price", "noData": "No share price data available"}, "isinWknWidget": {"title": "Identification", "isin": "ISIN", "wkn": "WKN", "valor": "VALOR", "displayValue": {"title": "Display value", "WKN": "WKN", "VALOR": "VALOR"}}, "dividendYieldWidget": {"title": "Current yield", "nextPayment": "Next dividend"}, "dividendEvolutionWidget": {"title": "Dividend evolution", "continuousIncreasesYears": "Years of continuous dividend increases", "noDividendsCuts": "Years with no dividend cuts", "dividendGrowth": "Dividend growth % over the past", "tenYears": "10 years", "since": "since", "noDividendPayments": "No dividend payments", "intervalDisplay": "{{start}} - {{end}} ", "displaySinceYear": "since {{start}}"}, "sectorWidget": {"title": "Company sectors", "noData": "No data available", "other": "Other"}, "error": {"simulation": "Failed to fetch simulation. Please try again later.", "insufficientData": "Insufficient Data for this scenario", "failedUpdate": "Failed to update data. Please try again."}, "dividendTableWidget": {"title": "All dividends", "exDate": "Ex-date", "payDate": "Pay date", "amount": "Yield", "yoy": "Year over year growth"}}