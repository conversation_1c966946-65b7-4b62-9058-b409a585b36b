/** @type {import('tailwindcss').Config} */

const { Colors } = require('./constants/Colors');

module.exports = {
  content: [
    './app/**/*.{js,jsx,ts,tsx}',
    './common/**/*.{js,jsx,ts,tsx}',
    './components/**/*.{js,jsx,ts,tsx}',
    './constants/**/*.{js,jsx,ts,tsx}',
    './hooks/**/*.{js,jsx,ts,tsx}',
  ],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      backgroundColor: {
        'primary-dark': Colors.lightBlue.background,
        'primary-light': Colors.lightBlue.background,
        'secondary-dark': Colors.lightBlue.background,
        'secondary-light': Colors.lightBlue.background,
      },
      colors: {
        theme: '#3939ff',
        muted: '#6c757d',
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      const newUtilities = {
        '.text-muted': {
          color: '#6c757d', // Default light mode color
        },
        '.dark .text-muted': {
          color: '#adb5bd !important', // Dark mode color
        },
      };
      addUtilities(newUtilities, ['responsive', 'hover']);
    },
  ],
};
