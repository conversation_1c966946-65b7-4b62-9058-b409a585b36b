import Purchases, { CustomerInfo, PERIOD_UNIT, PRODUCT_TYPE, PurchasesStoreProduct } from 'react-native-purchases';

export const productsMock: PurchasesStoreProduct[] = [
  {
    identifier: 'p-1',
    currencyCode: 'EUR',
    description: 'Unlimited access to Divizend companion.',
    // @ts-ignore
    defaultOption: {
      storeProductId: 'd-1',
      productId: 'subscription-1',
      id: 'op1',
      billingPeriod: { iso8601: 'P1M', unit: PERIOD_UNIT.MONTH, value: 1 },
      freePhase: null,
      isBasePlan: true,
      isPrepaid: true,
      tags: [],
      pricingPhases: [],
      fullPricePhase: null,
      installmentsInfo: null,
      introPhase: null,
      presentedOfferingContext: null,
    },
    discounts: [],
    introPrice: null,
    presentedOfferingContext: {
      offeringIdentifier: 'default',
      placementIdentifier: null,
      targetingContext: null,
    },
    price: 6.99,
    pricePerMonth: 6.99,
    pricePerMonthString: '6.99 €',
    priceString: '6.99 €',
    productCategory: Purchases.PRODUCT_CATEGORY.SUBSCRIPTION,
    productType: PRODUCT_TYPE.AUTO_RENEWABLE_SUBSCRIPTION,
    subscriptionPeriod: 'P1M',
    title: 'Monthly',
    pricePerWeek: 2,
    pricePerWeekString: '2',
    pricePerYear: 120,
    pricePerYearString: '120',
    subscriptionOptions: null,
  },
  {
    identifier: 'p-2',
    currencyCode: 'EUR',
    description: 'Unlimited access to Divizend companion for one year.',
    // @ts-ignore
    defaultOption: {
      storeProductId: 'd-2',
      productId: 'subscription-2',
      id: 'op2',
      billingPeriod: { iso8601: 'P1Y', unit: PERIOD_UNIT.YEAR, value: 1 },
      freePhase: null,
      isBasePlan: true,
      isPrepaid: true,
      tags: [],
      pricingPhases: [],
      fullPricePhase: null,
      installmentsInfo: null,
      introPhase: null,
      presentedOfferingContext: null,
    },
    discounts: [],
    introPrice: null,
    presentedOfferingContext: {
      offeringIdentifier: 'default',
      placementIdentifier: null,
      targetingContext: null,
    },
    price: 69.99,
    pricePerMonth: 5.83,
    pricePerMonthString: '5.83 €',
    priceString: '69.99 €',
    productCategory: Purchases.PRODUCT_CATEGORY.SUBSCRIPTION,
    productType: PRODUCT_TYPE.AUTO_RENEWABLE_SUBSCRIPTION,
    subscriptionPeriod: 'P1Y',
    title: 'Yearly',
    pricePerWeek: 1.35,
    pricePerWeekString: '1.35',
    pricePerYear: 69.99,
    pricePerYearString: '69.99',
    subscriptionOptions: null,
  },
];

export const adminCustomerInfo: any = {
  nonSubscriptionTransactions: [],
  originalPurchaseDate: null,
  allPurchaseDatesMillis: {
    'companion_basic_0:admin_membership': *************,
  },
  managementURL: 'https://play.google.com/store/account/subscriptions',
  allPurchaseDates: {
    'companion_basic_0:admin_membership': '2024-11-06T14:54:18.000Z',
  },
  originalAppUserId: '65d604e5f38a586135e224a5',
  allExpirationDates: {
    'companion_basic_0:admin_membership': '2024-11-06T14:57:16.000Z',
  },
  firstSeen: '2024-10-25T11:44:12.000Z',
  originalPurchaseDateMillis: null,
  allExpirationDatesMillis: {
    'companion_basic_0:admin_membership': *************,
  },
  requestDateMillis: *************,
  latestExpirationDate: '2024-11-06T14:57:16.000Z',
  firstSeenMillis: *************,
  allPurchasedProductIdentifiers: ['companion_basic_0:admin_membership'],
  requestDate: '2024-11-06T14:54:23.712Z',
  latestExpirationDateMillis: *************,
  originalApplicationVersion: null,
  activeSubscriptions: ['companion_basic_0:admin_membership'],
  entitlements: {
    active: {
      'divizend-membership': {
        billingIssueDetectedAtMillis: null,
        billingIssueDetectedAt: null,
        unsubscribeDetectedAtMillis: null,
        productIdentifier: 'companion_basic_0',
        unsubscribeDetectedAt: null,
        productPlanIdentifier: 'admin_membership',
        identifier: 'divizend-membership',
        isActive: true,
        periodType: 'TRIAL',
        store: 'PLAY_STORE',
        expirationDateMillis: *************,
        originalPurchaseDateMillis: *************,
        ownershipType: 'UNKNOWN',
        willRenew: true,
        latestPurchaseDate: '2024-11-06T14:54:18.000Z',
        expirationDate: '2024-11-06T14:57:16.000Z',
        verification: 'NOT_REQUESTED',
        originalPurchaseDate: '2024-11-06T14:54:18.000Z',
        isSandbox: true,
        latestPurchaseDateMillis: *************,
      },
    },
    verification: 'NOT_REQUESTED',
    all: {
      'divizend-membership': {
        billingIssueDetectedAtMillis: null,
        billingIssueDetectedAt: null,
        unsubscribeDetectedAtMillis: null,
        productIdentifier: 'companion_basic_0',
        unsubscribeDetectedAt: null,
        productPlanIdentifier: 'admin_membership',
        identifier: 'divizend-membership',
        isActive: true,
        periodType: 'TRIAL',
        store: 'PLAY_STORE',
        expirationDateMillis: *************,
        originalPurchaseDateMillis: *************,
        ownershipType: 'UNKNOWN',
        willRenew: true,
        latestPurchaseDate: '2024-11-06T14:54:18.000Z',
        expirationDate: '2024-11-06T14:57:16.000Z',
        verification: 'NOT_REQUESTED',
        originalPurchaseDate: '2024-11-06T14:54:18.000Z',
        isSandbox: true,
        latestPurchaseDateMillis: *************,
      },
    },
  },
};
