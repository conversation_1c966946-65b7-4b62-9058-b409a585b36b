import { LinearGradient } from 'expo-linear-gradient';
import { View, type ViewProps } from 'react-native';

import { useThemeColor, useThemeLinearGradient } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  useGradient?: boolean; // New prop to control gradient usage
};

export function ThemedView({ style, lightColor, darkColor, useGradient = true, ...otherProps }: ThemedViewProps) {
  const backgroundColor = useThemeColor({ light: lightColor, dark: darkColor }, 'background');
  const gradientColors = useThemeLinearGradient();
  console.log('ThemedView gradientColors:', gradientColors);
  console.log('ThemedView useGradient:', useGradient);
  if (useGradient) {
    return (
      <LinearGradient
        colors={gradientColors}
        style={[style]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        {...otherProps}
      >
        {otherProps.children}
      </LinearGradient>
    );
  }

  return <View style={[{ backgroundColor }, style]} {...otherProps} />;
}
