import { Platform, StyleSheet, Text, type TextProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';
import { scaleFont } from '@/utils/scaler';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'regular' | 'semi-bold' | 'bold';
  size?: number;
};

export function ThemedText({ style, lightColor, darkColor, type = 'regular', size, ...rest }: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  return (
    <Text
      style={[
        { color },
        type === 'regular' ? styles.default : undefined,
        type === 'semi-bold' ? styles.defaultSemiBold : undefined,
        type === 'bold' ? styles.defaultBold : undefined,
        {
          fontSize: Platform.OS === 'web' ? (size ?? 16) : scaleFont(size ?? 14),
        },
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontFamily: 'TintRegular',
  },
  defaultSemiBold: {
    fontFamily: 'TintSemiBold',
  },
  defaultBold: {
    fontFamily: 'TintBold',
  },
});
