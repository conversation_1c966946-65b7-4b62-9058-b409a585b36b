import React from 'react';

import { Icon } from '@rneui/base';
import { capitalize } from 'lodash';
import { useTranslation } from 'react-i18next';
import { StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';

import { clsx } from '@/common/clsx';
import { useUserProfileQuery } from '@/common/queries';
import { Text } from '@/components/base';
import { SelectModal } from '@/components/base/SelectModal';
import { BankParentIcon } from '@/components/features/portfolio-overview/BankParentIcon';
import { useThemeColorLegacy } from '@/hooks/useThemeColor';
import { actor } from '@/signals/actor';

interface PortfolioSelectorProps {
  style: StyleProp<ViewStyle>;
}

export const PortfolioSelector: React.FC<PortfolioSelectorProps> = ({ style }) => {
  const theme = useThemeColorLegacy();
  const { t } = useTranslation();
  const { data } = useUserProfileQuery();
  const depots = data?.depots ?? [];

  return (
    <SelectModal
      style={style}
      items={depots}
      labelExtractor={depot => depot.bankName ?? 'Depot'}
      label={actor.value.depotIds === 'all' ? t('actor.portfolioSelector.all') : undefined}
      modalTitle="Pick portfolios"
      searchPlaceholder="Search for portfolios..."
      selectedItems={
        depots?.filter(depot => actor.value.depotIds.includes(depot.id) || actor.value.depotIds === 'all') ?? []
      }
      onSelect={items => {
        if (items.length === 0) {
          actor.value = {
            ...actor.value,
            depotIds: 'all',
          };
          return;
        }
        actor.value = {
          ...actor.value,
          depotIds: items.map(item => item.id),
        };
      }}
      renderItem={(depot, isSelected, onPress) => (
        <TouchableOpacity onPress={onPress} className={clsx('flex-row items-center py-3 px-0 mb-2')}>
          {/* Left side - Checkbox and Bank Icon */}
          <View className="flex-row items-center mr-3">
            <Icon
              name={isSelected ? 'check-circle' : 'radio-button-unchecked'}
              type="material"
              size={20}
              color={isSelected ? theme.theme : theme.muted}
              style={{ marginRight: 12 }}
            />
            <BankParentIcon bankParent={depot.bankType} size={40} />
          </View>

          {/* Middle - Portfolio Info */}
          <View className="flex-1 ml-3">
            <Text className="font-medium text-base mb-1">{capitalize(depot.bankName)}</Text>
            <Text className="text-gray-500 text-sm">{depot.number || depot.description || depot.id}</Text>
          </View>

          {/* Right side - Arrow */}
          <Icon name="chevron-right" type="material" size={20} color={theme.muted} />
        </TouchableOpacity>
      )}
    />
  );
};
