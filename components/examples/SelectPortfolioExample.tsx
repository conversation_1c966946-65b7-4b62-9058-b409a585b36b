import React, { useState } from 'react';

import { View } from 'react-native';

import { Text } from '@/components/base';
import { SelectPortfolio } from '@/components/features/portfolio-overview';
import { UserProfileDepot } from '@/types/depot.types';

export const SelectPortfolioExample = () => {
  const [selectedPortfolios, setSelectedPortfolios] = useState<UserProfileDepot[]>([]);
  const [singlePortfolio, setSinglePortfolio] = useState<UserProfileDepot[]>([]);

  return (
    <View className="p-4 gap-8">
      <Text h1 className="mb-5">
        Select Portfolio Examples
      </Text>

      {/* Multiple Selection Example */}
      <View>
        <Text className="mb-2 font-medium">Multiple Portfolio Selection</Text>
        <SelectPortfolio
          selectedPortfolios={selectedPortfolios}
          onSelect={setSelectedPortfolios}
          multiple={true}
          placeholder="Select multiple portfolios"
        />

        {selectedPortfolios.length > 0 && (
          <View className="mt-2 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
            <Text className="font-medium mb-1">Selected Portfolios:</Text>
            {selectedPortfolios.map(portfolio => (
              <View key={portfolio.id} className="flex-row justify-between py-1">
                <Text>{portfolio.bankName}</Text>
                <Text className="text-gray-500">{portfolio.number}</Text>
              </View>
            ))}
          </View>
        )}
      </View>

      {/* Single Selection Example */}
      <View>
        <Text className="mb-2 font-medium">Single Portfolio Selection</Text>
        <SelectPortfolio
          selectedPortfolios={singlePortfolio}
          onSelect={setSinglePortfolio}
          multiple={false}
          placeholder="Select one portfolio"
        />

        {singlePortfolio.length > 0 && (
          <View className="mt-2 p-3 bg-green-50 dark:bg-green-900 rounded-lg">
            <Text className="font-medium mb-1">Selected Portfolio:</Text>
            <Text>
              {singlePortfolio[0].bankName} - {singlePortfolio[0].number}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};
