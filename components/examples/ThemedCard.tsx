import React from 'react';

import { LinearGradient } from 'expo-linear-gradient';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { useCurrentThemeColors, useThemeLinearGradient } from '@/hooks/useThemeColor';

interface ThemedCardProps {
  title: string;
  subtitle?: string;
  onPress?: () => void;
  children?: React.ReactNode;
  variant?: 'default' | 'gradient';
}

export function ThemedCard({ title, subtitle, onPress, children, variant = 'default' }: ThemedCardProps) {
  const colors = useCurrentThemeColors();
  const gradientColors = useThemeLinearGradient();

  const gradientContent = (
    <LinearGradient
      colors={gradientColors}
      style={[styles.card, styles.gradientCard]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Text style={[styles.title, { color: colors.background }]}>{title}</Text>
      {subtitle && <Text style={[styles.subtitle, { color: colors.background, opacity: 0.8 }]}>{subtitle}</Text>}
      {children}
    </LinearGradient>
  );

  const defaultContent = (
    <View
      style={[
        styles.card,
        {
          backgroundColor: colors.background,
          borderColor: colors.border,
        },
      ]}
    >
      <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
      {subtitle && <Text style={[styles.subtitle, { color: colors.text, opacity: 0.7 }]}>{subtitle}</Text>}
      {children}
    </View>
  );

  const content = variant === 'gradient' ? gradientContent : defaultContent;

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} style={styles.container}>
        {content}
      </TouchableOpacity>
    );
  }

  return <View style={styles.container}>{content}</View>;
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  card: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  gradientCard: {
    borderWidth: 0,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 8,
  },
});
