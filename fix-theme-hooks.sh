#!/bin/bash

# Script to fix all useThemeColor() calls to use useThemeColorLegacy()

echo "Fixing useThemeColor() imports and calls..."

# Find all files that import useThemeColor and use it without arguments
files=$(grep -r "useThemeColor()" --include="*.tsx" --include="*.ts" . | cut -d: -f1 | sort | uniq)

for file in $files; do
    echo "Processing: $file"
    
    # Replace import statement
    sed -i 's/import { useThemeColor }/import { useThemeColorLegacy }/g' "$file"
    sed -i 's/useThemeColor } from/useThemeColorLegacy } from/g' "$file"
    
    # Replace function calls
    sed -i 's/useThemeColor()/useThemeColorLegacy()/g' "$file"
    
    # Handle cases where there might be spaces
    sed -i 's/useThemeColor( )/useThemeColorLegacy()/g' "$file"
done

echo "Done! Fixed $(echo "$files" | wc -l) files."
echo "Files processed:"
echo "$files"
